"use client"

import Re<PERSON>, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Copy, CheckCircle, BookOpen, Sparkles, ChevronRight } from "lucide-react"
import { toast } from "sonner"
import { documentApi } from "@/lib/api"

interface Chapter {
  id: string
  title: string
  content: string
  subsections?: {
    title: string
    content: string
  }[]
}

interface ChaptersInterfaceProps {
  documentId?: number
}

export function ChaptersInterface({ documentId }: ChaptersInterfaceProps) {
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [copiedChapter, setCopiedChapter] = useState<string | null>(null)

  useEffect(() => {
    if (documentId) {
      loadChapters()
    }
  }, [documentId])

  const loadChapters = async () => {
    if (!documentId) {
      setError("No document selected")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const mockChapters: Chapter[] = [
        {
          id: "1",
          title: "Introduction to Machine Learning",
          content: "Machine learning is a subset of artificial intelligence that enables computers to learn and make decisions from data without being explicitly programmed. This chapter covers the fundamental concepts, types of machine learning, and real-world applications.",
          subsections: [
            {
              title: "What is Machine Learning?",
              content: "Definition, key concepts, and how ML differs from traditional programming approaches."
            },
            {
              title: "Types of Machine Learning",
              content: "Supervised, unsupervised, and reinforcement learning paradigms with examples."
            },
            {
              title: "Applications in Industry",
              content: "Real-world use cases across healthcare, finance, technology, and other sectors."
            }
          ]
        },
        {
          id: "2",
          title: "Supervised Learning Algorithms",
          content: "Supervised learning involves training models on labeled data to make predictions on new, unseen data. This chapter explores various supervised learning algorithms including regression and classification techniques.",
          subsections: [
            {
              title: "Linear Regression",
              content: "Understanding linear relationships, least squares method, and model evaluation."
            },
            {
              title: "Decision Trees",
              content: "Tree-based models, splitting criteria, pruning, and handling overfitting."
            },
            {
              title: "Support Vector Machines",
              content: "Margin maximization, kernel trick, and applications in classification."
            },
            {
              title: "Ensemble Methods",
              content: "Random forests, gradient boosting, and combining multiple models."
            }
          ]
        },
        {
          id: "3",
          title: "Unsupervised Learning Techniques",
          content: "Unsupervised learning discovers hidden patterns in data without labeled examples. This chapter covers clustering, dimensionality reduction, and association rule mining.",
          subsections: [
            {
              title: "Clustering Algorithms",
              content: "K-means, hierarchical clustering, and DBSCAN for grouping similar data points."
            },
            {
              title: "Dimensionality Reduction",
              content: "PCA, t-SNE, and UMAP for visualizing and compressing high-dimensional data."
            },
            {
              title: "Association Rules",
              content: "Market basket analysis and finding relationships between variables."
            }
          ]
        },
        {
          id: "4",
          title: "Deep Learning and Neural Networks",
          content: "Deep learning uses neural networks with multiple layers to learn complex patterns. This chapter introduces neural network architectures and their applications.",
          subsections: [
            {
              title: "Neural Network Fundamentals",
              content: "Perceptrons, activation functions, and backpropagation algorithm."
            },
            {
              title: "Convolutional Neural Networks",
              content: "CNN architecture for image recognition and computer vision tasks."
            },
            {
              title: "Recurrent Neural Networks",
              content: "RNNs, LSTMs, and GRUs for sequential data and time series analysis."
            },
            {
              title: "Transfer Learning",
              content: "Leveraging pre-trained models and fine-tuning for specific tasks."
            }
          ]
        },
        {
          id: "5",
          title: "Model Evaluation and Deployment",
          content: "Proper evaluation and deployment are crucial for successful machine learning projects. This chapter covers validation techniques, performance metrics, and production considerations.",
          subsections: [
            {
              title: "Cross-Validation",
              content: "K-fold validation, stratified sampling, and avoiding data leakage."
            },
            {
              title: "Performance Metrics",
              content: "Accuracy, precision, recall, F1-score, and choosing appropriate metrics."
            },
            {
              title: "Model Deployment",
              content: "MLOps, model serving, monitoring, and maintaining models in production."
            },
            {
              title: "Ethical Considerations",
              content: "Bias detection, fairness metrics, and responsible AI practices."
            }
          ]
        }
      ]

      setChapters(mockChapters)
    } catch (err) {
      console.error('Error loading chapters:', err)
      setError('Failed to load chapters. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const generateNewChapters = async () => {
    if (!documentId) {
      toast.error("No document selected for chapter generation")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Simulate generating new chapters with different content
      const newChapters: Chapter[] = [
        {
          id: "1",
          title: "Foundations of Artificial Intelligence",
          content: "Explore the historical development and core principles of AI, from early symbolic systems to modern machine learning approaches.",
          subsections: [
            {
              title: "History of AI",
              content: "Timeline of AI development from the 1950s to present day."
            },
            {
              title: "AI vs ML vs DL",
              content: "Understanding the relationships between artificial intelligence, machine learning, and deep learning."
            }
          ]
        },
        {
          id: "2",
          title: "Data Preprocessing and Feature Engineering",
          content: "Learn essential techniques for preparing data and creating meaningful features for machine learning models.",
          subsections: [
            {
              title: "Data Cleaning",
              content: "Handling missing values, outliers, and inconsistent data."
            },
            {
              title: "Feature Selection",
              content: "Methods for identifying the most relevant features for your model."
            }
          ]
        }
      ]

      setChapters(newChapters)
      toast.success("New chapters generated successfully!")
    } catch (err) {
      console.error('Error generating chapters:', err)
      setError('Failed to generate new chapters. Please try again.')
      toast.error("Failed to generate new chapters")
    } finally {
      setIsLoading(false)
    }
  }

  const copyChapterToClipboard = async (chapter: Chapter) => {
    let content = `# ${chapter.title}\n\n${chapter.content}\n\n`
    
    if (chapter.subsections) {
      chapter.subsections.forEach(subsection => {
        content += `## ${subsection.title}\n${subsection.content}\n\n`
      })
    }

    try {
      await navigator.clipboard.writeText(content)
      setCopiedChapter(chapter.id)
      toast.success(`Chapter "${chapter.title}" copied to clipboard!`)
      
      setTimeout(() => setCopiedChapter(null), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
      toast.error("Failed to copy chapter")
    }
  }

  const copyAllChapters = async () => {
    if (chapters.length === 0) return

    let allContent = ""
    chapters.forEach((chapter, index) => {
      allContent += `# Chapter ${index + 1}: ${chapter.title}\n\n${chapter.content}\n\n`
      
      if (chapter.subsections) {
        chapter.subsections.forEach(subsection => {
          allContent += `## ${subsection.title}\n${subsection.content}\n\n`
        })
      }
      
      allContent += "---\n\n"
    })

    try {
      await navigator.clipboard.writeText(allContent)
      toast.success("All chapters copied to clipboard!")
    } catch (err) {
      console.error('Failed to copy:', err)
      toast.error("Failed to copy all chapters")
    }
  }

  if (!documentId) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Document Chapters
            </CardTitle>
            <CardDescription>
              Select a document to view its chapter breakdown
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Please select a document to generate chapters.
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Document Chapters
            </CardTitle>
            <CardDescription>
              {chapters.length > 0 ? "Generating new chapters..." : "Loading chapters..."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Document Chapters
            </CardTitle>
            <CardDescription className="text-red-500">
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={loadChapters} className="w-full">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Document Chapters
          </CardTitle>
          <CardDescription>
            AI-generated chapter breakdown of your document
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 mb-4">
            <Button
              onClick={copyAllChapters}
              variant="outline"
              className="flex-1"
              disabled={chapters.length === 0}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy All Chapters
            </Button>
            <Button
              onClick={generateNewChapters}
              className="flex-1"
              disabled={isLoading}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              Generate New Chapters
            </Button>
          </div>

          <Accordion type="single" collapsible className="w-full">
            {chapters.map((chapter, index) => (
              <AccordionItem key={chapter.id} value={chapter.id}>
                <AccordionTrigger className="text-left">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-muted-foreground">
                      Chapter {index + 1}
                    </span>
                    <ChevronRight className="h-4 w-4" />
                    <span>{chapter.title}</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-4 pt-2">
                    <p className="text-sm leading-relaxed">{chapter.content}</p>
                    
                    {chapter.subsections && chapter.subsections.length > 0 && (
                      <div className="space-y-3">
                        <h4 className="font-medium text-sm">Subsections:</h4>
                        {chapter.subsections.map((subsection, subIndex) => (
                          <div key={subIndex} className="pl-4 border-l-2 border-muted">
                            <h5 className="font-medium text-sm mb-1">{subsection.title}</h5>
                            <p className="text-sm text-muted-foreground">{subsection.content}</p>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    <Button
                      onClick={() => copyChapterToClipboard(chapter)}
                      variant="outline"
                      size="sm"
                      className="mt-3"
                    >
                      {copiedChapter === chapter.id ? (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Chapter
                        </>
                      )}
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>
    </div>
  )
}
