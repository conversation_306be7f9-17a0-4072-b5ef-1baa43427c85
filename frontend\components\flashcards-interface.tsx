"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Plus, Trash2, ChevronRight, RotateCcw, MessageSquare } from "lucide-react"
import { motion } from "framer-motion"
import { useTheme } from "@/components/theme-provider"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { flashcardsApi } from "@/lib/api"

interface Flashcard {
  id: string
  question: string
  answer: string
}

interface FlashcardsInterfaceProps {
  documentId?: number
}

export function FlashcardsInterface({ documentId }: FlashcardsInterfaceProps) {
  const [flashcards, setFlashcards] = useState<Flashcard[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isFlipped, setIsFlipped] = useState(false)
  const [showAddForm, setShowAddForm] = useState(false)
  const [newQuestion, setNewQuestion] = useState("")
  const [newAnswer, setNewAnswer] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const { theme } = useTheme()
  const flashcardsContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadFlashcards()
  }, [documentId])

  const loadFlashcards = async () => {
    if (!documentId) {
      console.warn('No document ID provided for flashcards')
      return
    }

    try {
      setIsLoading(true)

      // First, try to get existing flashcards
      try {
        const existingFlashcards = await flashcardsApi.getFlashcards(documentId)
        if (existingFlashcards && existingFlashcards.length > 0) {
          const formattedFlashcards = existingFlashcards.map((card: any) => ({
            id: card.id.toString(),
            question: card.front,
            answer: card.back
          }))
          setFlashcards(formattedFlashcards)
          return
        }
      } catch (existingError) {
        console.log('No existing flashcards found, will generate new ones')
      }

      // If no existing flashcards, generate new ones
      const response = await flashcardsApi.generateFlashcards(documentId)
      const formattedFlashcards = response.flashcards.map((card: any, index: number) => ({
        id: index.toString(),
        question: card.front,
        answer: card.back
      }))
      setFlashcards(formattedFlashcards)
    } catch (error) {
      console.error('Error loading flashcards:', error)
      // Don't set any fallback flashcards - show empty state instead
      setFlashcards([])
    } finally {
      setIsLoading(false)
    }
  }

  const currentCard = flashcards[currentIndex]

  const handleFlip = () => {
    setIsFlipped(!isFlipped)
  }

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % flashcards.length)
    setIsFlipped(false)
  }

  const handleDelete = () => {
    if (flashcards.length > 1) {
      const newFlashcards = flashcards.filter((_, index) => index !== currentIndex)
      setFlashcards(newFlashcards)
      setCurrentIndex((prev) => (prev >= newFlashcards.length ? 0 : prev))
      setIsFlipped(false)
    }
  }

  const handleAddFlashcard = () => {
    if (newQuestion.trim() && newAnswer.trim()) {
      const newCard: Flashcard = {
        id: Date.now().toString(),
        question: newQuestion.trim(),
        answer: newAnswer.trim(),
      }
      setFlashcards([...flashcards, newCard])
      setNewQuestion("")
      setNewAnswer("")
      setShowAddForm(false)
    }
  }

  const resetToFirst = () => {
    setCurrentIndex(0)
    setIsFlipped(false)
  }

  const isEmpty = flashcards.length === 0

  return (
    <div className="flex flex-col h-full bg-black" ref={flashcardsContainerRef}>
      <div className="p-4 border-b border-neutral-800">
        <h2 className="text-xl font-medium text-center">Flashcards</h2>
        {!isEmpty && !showAddForm && (
          <p className="text-center text-sm text-muted-foreground mt-1">
            Card {currentIndex + 1} of {flashcards.length}
          </p>
        )}
      </div>

      <ScrollArea className="flex-1">
        {isLoading ? (
          <div className="h-full flex flex-col items-center justify-center p-4">
            <div className="p-6 rounded-full bg-neutral-800 mb-4">
              <div className="h-12 w-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin" />
            </div>
            <div className="rounded-lg p-4 bg-purple-600/10 border border-purple-600/30 max-w-md text-center">
              <h3 className="font-medium text-purple-500 mb-2">Loading Flashcards</h3>
              <p className="text-sm">
                {!documentId
                  ? "No document selected"
                  : "Checking for existing flashcards or generating new ones..."
                }
              </p>
            </div>
          </div>
        ) : isEmpty ? (
          <div className="h-full flex flex-col items-center justify-center p-4">
            <div className="p-6 rounded-full bg-neutral-800 mb-4">
              <MessageSquare className="h-12 w-12 text-muted-foreground" />
            </div>
            <div className="rounded-lg p-4 bg-purple-600/10 border border-purple-600/30 max-w-md text-center">
              <h3 className="font-medium text-purple-500 mb-2">No Flashcards Available</h3>
              <p className="text-sm">
                {!documentId
                  ? "Please select a document to view flashcards."
                  : "Unable to load or generate flashcards for this document. Please try again or create flashcards manually."
                }
              </p>
              {documentId && (
                <button
                  onClick={loadFlashcards}
                  className="mt-3 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-md transition-colors"
                >
                  Try Again
                </button>
              )}
            </div>
          </div>
        ) : showAddForm ? (
          <div className="p-6">
            <div className="max-w-lg mx-auto">
              <h3 className="text-lg font-medium mb-4">Add New Flashcard</h3>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="question">Question</Label>
                  <Textarea
                    id="question"
                    placeholder="Enter the flashcard question"
                    value={newQuestion}
                    onChange={(e) => setNewQuestion(e.target.value)}
                    className="min-h-[100px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="answer">Answer</Label>
                  <Textarea
                    id="answer"
                    placeholder="Enter the flashcard answer"
                    value={newAnswer}
                    onChange={(e) => setNewAnswer(e.target.value)}
                    className="min-h-[150px] bg-neutral-800 border-neutral-700 focus-visible:ring-purple-500"
                  />
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowAddForm(false)}>
                    Cancel
                  </Button>
                  <Button
                    className="bg-purple-600 hover:bg-purple-700"
                    onClick={handleAddFlashcard}
                    disabled={!newQuestion.trim() || !newAnswer.trim()}
                  >
                    Add Flashcard
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="p-6 flex flex-col items-center">
            {/* Flashcard */}
            <div className="w-full max-w-lg mb-8">
              <div className="relative w-full perspective-1000">
                <motion.div
                  className="w-full relative preserve-3d"
                  animate={{ rotateY: isFlipped ? 180 : 0 }}
                  transition={{ duration: 0.6, type: "spring" }}
                  style={{ transformStyle: "preserve-3d" }}
                >
                  {/* Front of card (Question) */}
                  <Card
                    className="absolute inset-0 backface-hidden bg-neutral-800 border-neutral-700 p-6 cursor-pointer"
                    onClick={handleFlip}
                  >
                    <div className="flex flex-col h-full">
                      <div className="text-sm text-neutral-400 mb-2">QUESTION</div>
                      <div className="flex-1 flex items-center justify-center">
                        <p className="text-lg">{currentCard?.question}</p>
                      </div>
                      <div className="text-xs text-neutral-500 mt-4 text-center">Click to reveal answer</div>
                    </div>
                  </Card>

                  {/* Back of card (Answer) */}
                  <Card
                    className="absolute inset-0 backface-hidden bg-neutral-800 border-neutral-700 p-6 cursor-pointer"
                    onClick={handleFlip}
                    style={{ transform: "rotateY(180deg)" }}
                  >
                    <div className="flex flex-col h-full">
                      <div className="text-sm text-neutral-400 mb-2">ANSWER</div>
                      <div className="flex-1 flex items-center justify-center">
                        <p className="text-lg">{currentCard?.answer}</p>
                      </div>
                      <div className="text-xs text-neutral-500 mt-4 text-center">Click to see question</div>
                    </div>
                  </Card>
                </motion.div>
              </div>
            </div>

            {/* Control Buttons */}
            <div className="flex flex-wrap justify-center gap-3">
              <Button onClick={handleNext} className="bg-purple-600 hover:bg-purple-700">
                Next Card
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>

              <Button onClick={() => setShowAddForm(true)} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Card
              </Button>

              <Button onClick={handleDelete} variant="outline" disabled={flashcards.length <= 1}>
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Card
              </Button>

              <Button onClick={resetToFirst} variant="outline">
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        )}
      </ScrollArea>

      <style jsx global>{`
        .perspective-1000 {
          perspective: 1000px;
        }
        .preserve-3d {
          transform-style: preserve-3d;
          min-height: 300px;
        }
        .backface-hidden {
          backface-visibility: hidden;
        }
      `}</style>
    </div>
  )
} 