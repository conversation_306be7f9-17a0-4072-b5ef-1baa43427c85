from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import numpy as np

class UserInfo(BaseModel):
    """User information for authentication and tracking"""
    id: int
    username: str
    email: Optional[str] = None
    token: Optional[str] = None  # Store the authentication token for use in subsequent requests

class InferenceRequest(BaseModel):
    """Request model for LLM inference"""
    model: str
    message: str
    context: Optional[str] = None

    class Config:
        schema_extra = {
            "example": {
                "model": "openai",
                "message": "What is the capital of France?",
                "context": "You are a helpful assistant."
            }
        }

class InferenceResponse(BaseModel):
    """Response model for LLM inference"""
    response: str
    model: str
    tokens: int
    usage_stats: Optional[Dict[str, Any]] = None

    class Config:
        schema_extra = {
            "example": {
                "response": "The capital of France is Paris.",
                "model": "openai",
                "tokens": 10,
                "usage_stats": None
            }
        }

class DocumentProcessRequest(BaseModel):
    """Request model for document processing"""
    document_id: int
    user_id: int

class DocumentProcessResponse(BaseModel):
    """Response model for document processing"""
    message: str
    document_id: int
    num_chunks: int
    status: str

class BlueprintProcessRequest(BaseModel):
    """Request model for blueprint processing"""
    document_id: int
    user_id: int
    blueprint_text: str
    llm_model: str = "openai"  # Default to OpenAI, but can be changed to "gemini"

class BlueprintTopic(BaseModel):
    """Model for a blueprint topic"""
    title: str
    weightage: float

class BlueprintProcessResponse(BaseModel):
    """Response model for blueprint processing"""
    message: str
    document_id: int
    topics: List[BlueprintTopic]
    status: str

class TextChunk(BaseModel):
    """Model for a text chunk with its embedding"""
    text: str
    embedding: List[float]
    chunk_number: int

class DocumentEmbedding(BaseModel):
    """Model for document embeddings"""
    document_id: int
    chunks: List[TextChunk]

class QuizQuestion(BaseModel):
    """Model for a quiz question and answer"""
    question: str
    answer: str

class QuizGenerationRequest(BaseModel):
    """Request model for quiz generation"""
    document_id: int
    llm_model: str = "openai"  # Default to OpenAI, but can be changed to "gemini"
    num_questions: int = 5  # Default to 5 questions

    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "llm_model": "openai",
                "num_questions": 5
            }
        }

class QuizGenerationResponse(BaseModel):
    """Response model for quiz generation"""
    message: str
    document_id: int
    questions: List[QuizQuestion]
    status: str
    model: str
    tokens: int

class Flashcard(BaseModel):
    """Model for a flashcard with front and back content"""
    front: str
    back: str

class FlashcardGenerationRequest(BaseModel):
    """Request model for flashcard generation"""
    document_id: int
    llm_model: str = "openai"  # Default to OpenAI, but can be changed to "gemini"
    num_flashcards: int = 10  # Default to 10 flashcards

    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "llm_model": "openai",
                "num_flashcards": 10
            }
        }

class FlashcardGenerationResponse(BaseModel):
    """Response model for flashcard generation"""
    message: str
    document_id: int
    flashcards: List[Flashcard]
    status: str
    model: str
    tokens: int
