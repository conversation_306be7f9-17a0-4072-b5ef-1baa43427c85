from rest_framework import viewsets, status, filters
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import AllowAny, IsAuthenticated
from django.core.exceptions import PermissionDenied
from .models import Student, UserUsage, StudentPerformance
from .serializers import (
    StudentSerializer, StudentRegistrationSerializer, EmailVerificationSerializer,
    GoogleAuthSerializer, OTPVerificationSerializer, StudentPerformanceSerializer
)
from rest_framework.decorators import api_view, permission_classes
from .utils import get_usage_stats
from django.conf import settings
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from django.utils import timezone
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.authtoken.models import Token

class StudentViewSet(viewsets.ModelViewSet):
    queryset = Student.objects.all()
    serializer_class = StudentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Student.objects.filter(id=self.request.user.id)

class StudentRegistrationView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = StudentRegistrationSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            student = serializer.save()
            return Response({
                'message': 'Please check your email for OTP verification.',
                'email': student.email
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class EmailVerificationView(APIView):
    permission_classes = [AllowAny]

    def post(self, request, token):
        serializer = EmailVerificationSerializer(data={'token': token})
        if serializer.is_valid():
            student = Student.objects.get(email_verification_token=token)

            # Check if token is expired (24 hour validity)
            if timezone.now() > student.email_verification_sent_at + timezone.timedelta(days=1):
                return Response({
                    'error': 'Verification token has expired'
                }, status=status.HTTP_400_BAD_REQUEST)

            student.is_email_verified = True
            student.email_verification_token = None
            student.save()
            return Response({
                'message': 'Email verified successfully'
            }, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class OTPVerificationView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = OTPVerificationSerializer(data=request.data)
        if serializer.is_valid():
            student = Student.objects.get(email=serializer.validated_data['email'])

            # Activate user and mark email as verified
            student.is_active = True
            student.is_email_verified = True
            student.otp = None
            student.otp_created_at = None
            student.save()

            return Response({
                'message': 'Account verified successfully. You can now login.'
            }, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_usage(request):
    try:
        usage_stats = get_usage_stats(request.user)
        return Response(usage_stats)
    except PermissionDenied:
        return Response(
            {"error": "Authentication required"},
            status=status.HTTP_401_UNAUTHORIZED
        )
    except Exception as e:
        return Response(
            {"error": f"Error retrieving usage stats: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

class GoogleSignInView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = GoogleAuthSerializer(data=request.data)
        if serializer.is_valid():
            idinfo = serializer.validated_data['token']

            try:
                # Get or create user
                student, created = Student.objects.get_or_create(
                    email=idinfo['email'],
                    defaults={
                        'first_name': idinfo.get('given_name', ''),
                        'last_name': idinfo.get('family_name', ''),
                        'is_email_verified': True,
                    }
                )

                # Generate JWT tokens
                refresh = RefreshToken.for_user(student)

                return Response({
                    'message': 'Successfully authenticated with Google',
                    'access_token': str(refresh.access_token),
                    'refresh_token': str(refresh),
                    'user': StudentSerializer(student).data
                }, status=status.HTTP_200_OK)

            except Exception as e:
                return Response({
                    'error': 'Authentication failed',
                    'details': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class CustomAuthToken(ObtainAuthToken):
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data,
                                         context={'request': request})
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        token, created = Token.objects.get_or_create(user=user)
        return Response({
            'token': token.key,
            'user_id': user.pk,
            'email': user.email
        })


class StudentPerformanceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing student performance records.
    """
    serializer_class = StudentPerformanceSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['document__title', 'student__username']
    ordering_fields = ['quiz_score', 'attempts', 'created_at', 'updated_at']
    ordering = ['-updated_at']  # Default ordering

    def get_queryset(self):
        """
        Filter performances based on user role:
        - Students can only see their own performances
        - Staff/admin can see all performances
        """
        user = self.request.user
        if user.is_staff or user.is_superuser:
            return StudentPerformance.objects.all()
        return StudentPerformance.objects.filter(student=user)

    def perform_create(self, serializer):
        """Set the student to the current user if not specified"""
        if not self.request.user.is_staff and not self.request.user.is_superuser:
            serializer.save(student=self.request.user)
        else:
            serializer.save()
