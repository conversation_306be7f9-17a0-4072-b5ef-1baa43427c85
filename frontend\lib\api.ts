import axios from 'axios';

// Base URL for API requests
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Auth API endpoints
export const authApi = {
  signIn: async (credentials: { username: string; password: string }) => {
    try {
      const response = await api.post('/users/login/', credentials);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  register: async (userData: {
    username: string;
    email: string;
    password: string;
    confirm_password: string;
    first_name: string;
    last_name: string;
  }) => {
    try {
      const response = await api.post('/users/register/', userData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  verifyOTP: async (data: { email: string; otp: string }) => {
    try {
      const response = await api.post('/users/verify-otp/', data);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  logout: async () => {
    try {
      const response = await api.post('/users/logout/');
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Document API endpoints
export const documentApi = {
  uploadDocument: async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await api.post('/documents/upload/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getDocuments: async () => {
    try {
      const response = await api.get('/documents/');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getDocumentStatus: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/${documentId}/`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// Chat API endpoints
export const chatApi = {
  sendMessage: async (message: string, documentId?: string) => {
    try {
      const response = await api.post('/chat/message/', {
        message,
        document_id: documentId,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getHistory: async () => {
    try {
      const response = await api.get('/chat/history/');
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};


// Performance API endpoints
export const performanceApi = {
  createPerformance: async (performanceData: {
    student: number;
    document: number;
    quiz_score: number;
    time_taken: number;
    remarks?: string;
  }) => {
    try {
      const response = await api.post('/users/performance/', performanceData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getPerformances: async (documentId?: number) => {
    try {
      const url = documentId
        ? `/users/performance/?document=${documentId}`
        : '/users/performance/';
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getStudentPerformances: async (studentId: number) => {
    try {
      const response = await api.get(`/users/performance/?student=${studentId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  getPerformanceStats: async (documentId: number) => {
    try {
      const response = await api.get(`/users/performance/?document=${documentId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

}
// Blueprint API (process blueprint)
export const processBlueprint = async (
  documentId: number,
  blueprintText: string,
  llmModel: string = 'openai' // can be 'gemini', 'rag', 'openai', etc.
) => {
  try {
    const response = await api.post(`/process-blueprint/${documentId}/`, {
      document_id: documentId,
      blueprint_text: blueprintText,
      llm_model: llmModel,
    });
    return response.data;
  } catch (error) {
    console.error('Error processing blueprint:', error);
    throw error;
  }
};

// Flashcards API
export const flashcardsApi = {
  // Get existing flashcards for a document
  getFlashcards: async (documentId: number) => {
    try {
      const response = await api.get(`/documents/flashcards/?document=${documentId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching flashcards:', error);
      throw error;
    }
  },

  // Generate new flashcards for a document
  generateFlashcards: async (
    documentId: number,
    numFlashcards: number = 10,
    llmModel: string = 'openai'
  ) => {
    try {
      const response = await api.post(`/flashcards/${documentId}/`, {
        num_flashcards: numFlashcards,
        llm_model: llmModel,
      });
      return response.data;
    } catch (error) {
      console.error('Error generating flashcards:', error);
      throw error;
    }
  }
};

// Legacy function for backward compatibility
export const generateFlashcards = flashcardsApi.generateFlashcards;

// Flowchart API
export const generateFlowchart = async (
  documentId: number,
  llmModel: string = 'openai'
) => {
  try {
    const response = await api.post(`/flowchart/${documentId}/`, {
      llm_model: llmModel,
    });
    return response.data;
  } catch (error) {
    console.error('Error generating flowchart:', error);
    throw error;
  }
};

// Quiz API
export const quizApi = {
  generateQuiz: async (
    documentId: number,
    numQuestions: number = 5,
    llmModel: string = 'openai'
  ) => {
    try {
      const response = await api.post(`/quizzes/${documentId}/`, {
        num_questions: numQuestions,
        llm_model: llmModel,
      });
      return response.data;
    } catch (error) {
      console.error('Error generating quiz:', error);
      throw error;
    }
  },

  getQuizzes: async (documentId: number) => {
    try {
      const response = await api.get(`/quizzes/?document=${documentId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      throw error;
    }
  },

  submitQuizAnswer: async (quizId: number, answers: any) => {
    try {
      const response = await api.post(`/quizzes/${quizId}/submit/`, { answers });
      return response.data;
    } catch (error) {
      console.error('Error submitting quiz:', error);
      throw error;
    }
  }
};

// Legacy export for backward compatibility
export const generateQuiz = quizApi.generateQuiz;

export default api;
